from django.db import models
from core.model import BaseModel


class MideaStaff(BaseModel):
    # 名字
    name = models.CharField(max_length=32, verbose_name='姓名')
    # 电话
    phone = models.CharField(max_length=11, verbose_name='手机号')
    # 密码
    password = models.CharField(max_length=128, verbose_name='密码')
    # 角色
    role = models.JSONField(verbose_name='角色',default=list)

    # TODO: 待补充

    class Meta:
        verbose_name = '用户'
        verbose_name_plural = '用户'

    def __str__(self):
        return self.name
