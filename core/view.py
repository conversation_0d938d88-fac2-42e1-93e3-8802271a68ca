from django.core.exceptions import FieldError
from django.db.models import Q
from rest_framework.views import APIView
from core.resp import make_response
from user.models import Staff


# 分页列表视图
class PaginationListBaseView(APIView):

    authentication_classes = []
    permission_classes = []
    staff_required_permission = None
    serializer_class = None
    response_msg = ""
    error_response_msg = ""
    search_fields = []
    additional_data = None
    audit_log_message = ""
    
    def get_additional_data(self):
        return None

    def get_queryset(self):
        return None
    
    def update_fields(self,queryset):
        return
    
    def get(self, request):
        queryset = self.get_queryset()
        
        # 当queryset为None时表示有错误，而空QuerySet表示有效查询但无数据
        if queryset is None:
            return make_response(code=-1, msg=self.error_response_msg)
        
        try:
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 10))
        except ValueError:
            page = 1
            page_size = 10
        
        sk = request.query_params.get('sk', None)
        
        if sk and self.search_fields:
            query = Q()
            search_fields = self.search_fields
            for field in search_fields:
                try:
                    query |= Q(**{f"{field}__icontains": sk})
                except FieldError:
                    pass
            if query:
                queryset = queryset.filter(query)
        
        total_count = queryset.count()
        total_page = (total_count + page_size - 1) // page_size
        start = (page - 1) * page_size
        end = start + page_size
        
        # 获取分页数据
        paginated_applications = queryset[start:end]
        
        serializer = self.serializer_class(paginated_applications, many=True,context={'request':request})
        
        # 构建返回数据格式
        result = {
            'list': serializer.data,
            'page': page,
            'page_size': page_size,
            'total_count': total_count,
            'total_page': total_page
        }
        
        additional_data = self.get_additional_data()
        if additional_data:
            result['additional_data'] = additional_data
        
        self.update_fields(queryset)
        
        #if self.audit_log_message and isinstance(request.user, Staff):
        #        AuditLogCreator.create_query_audit_log(request, self.audit_log_message, f"查看了<{self.audit_log_message}>列表")
        
        return make_response(code=0, msg=self.response_msg, data=result)