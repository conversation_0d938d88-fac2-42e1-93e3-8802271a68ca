from rest_framework.response import Response




def make_response(data=None, code: int = 0, msg: str = ''):
    # if code < 0 and isinstance(msg, dict):
    #     def extract_first_error(error_dict):
    #         for field, errors in error_dict.items():
    #             if isinstance(errors, list) and errors:
    #                 return errors[0]
    #             elif isinstance(errors, dict):
    #                 nested_error = extract_first_error(errors)
    #                 if nested_error:
    #                     return nested_error
    #         return None

    #     first_error = extract_first_error(msg)
    #     if first_error:
    #         print(msg)
    #         return Response({
    #             'code': -1,
    #             'msg': "数据校验失败",
    #             'data': None,
    #         })

    return Response({
        'code': code,
        'msg': msg,
        'data': data,
    })