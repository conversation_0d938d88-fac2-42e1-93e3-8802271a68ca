Capacity_table = {
    "LTHX":      [5000, 4000, 3000, 2000, 1600],
    "LTHX Car":  [5000, 4000, 3000],
    "LTHW":      [5000, 4000, 3000, 2000, 1600],
    "LTHW Car":  [5000, 4000, 3000],
    "EVIK":      [320, 400, 450, 630, 800, 1000, 1150, 1250, 1350, 1600, 1800, 2000],
    "EVIN":      [320, 400, 450, 630, 800, 1000, 1150, 1250, 1350, 1600, 1800, 2000],
    "LTK":       [630, 800, 1000, 1150, 1250, 1350, 1600, 2000],
}

def get_min_max_capacity(Lift_Model):
    capacities = Capacity_table.get(Lift_Model)
    return min(capacities), max(capacities)