width_rules = {
    'a': [
        (lambda cap: cap <= 2000, 1400),
        (lambda cap: cap <= 3000, 1800),
        (lambda cap: cap <= 5000, 2000),
        (lambda cap: cap > 5000, None)  # ### 表示无定义或超出范围
    ],
    'b': [
        (lambda cap: cap <= 3000, 4000),
        (lambda cap: cap > 3000, 5900)
    ],
    'c': [
        (lambda cap: cap < 630, 1000),
        (lambda cap: cap >= 630, 1100)
    ],
    'd': [
        (lambda cap: cap <= 800, 900),
        (lambda cap: cap <= 1050, 1000),
        (lambda cap: cap <= 1250, 1100),
        (lambda cap: cap <= 1350, 1200),
        (lambda cap: cap <= 1600, 1300),
        (lambda cap: cap <= 2000, 1400),
        (lambda cap: cap > 2000, None)  # ### 表示无定义或超出范围
    ]
}

# 轿厢宽度/深度映射表
car_dimension_rules = {
    "LTHX": {
        "min_width": "a",
        "max_width": None,
        "min_depth": 1500,
        "max_depth": None
    },
    "LTHX Car": {
        "min_width": 2000,
        "max_width": None,
        "min_depth": "b",
        "max_depth": None
    },
    "LTHW": {
        "min_width": "a",
        "max_width": None,
        "min_depth": 1600,
        "max_depth": None
    },
    "LTHW Car": {
        "min_width": 2000,
        "max_width": None,
        "min_depth": 4000,
        "max_depth": None
    },
    "EVIK": {
        "min_width": "d",
        "max_width": 2200,
        "min_depth": "c",
        "max_depth": 2600
    },
    "EVIN": {
        "min_width": "d",
        "max_width": 2200,
        "min_depth": "c",
        "max_depth": 2600
    },
    "LTK": {
        "min_width": 900,
        "max_width": 2200,
        "min_depth": 900,
        "max_depth": 2600
    }
}

def get_width_depth(Lift_Model, Capacity, component):
    rule_key = car_dimension_rules[Lift_Model][component]
    if isinstance(rule_key, int):  # 固定值
        return rule_key
    elif isinstance(rule_key, str) and rule_key in width_rules:
        for cond, val in width_rules[rule_key]:
            if cond(Capacity):
                return val
    return None