lookup_rules_SD0 = {
    "R1": [
        (lambda door, std, spd: door in ["C2", "S2", "C4"], 2550),
        (lambda door, std, spd: door in ["S3", "C6"], 2650)
    ],
    "R2": [
        (lambda door, std, spd: door in ["C2", "S2", "C4"], 2950),
        (lambda door, std, spd: door in ["S3", "C6"], 3050)
    ],
    "R3": [
        (lambda door, std, spd: door in ["C2", "S2", "C4"], 3250),
        (lambda door, std, spd: door in ["S3", "C6"], 3350)
    ],
    "R4": [
        (lambda door, std, spd: door in ["C2", "S2", "C4"], 3550),
        (lambda door, std, spd: door in ["S3", "C6"], 3650)
    ],
    "R5": [
        (lambda door, std, spd: std != "GOST 33984.1", 1550),
        (lambda door, std, spd: std == "GOST 33984.1", 1570)
    ],
    "R6": [
        (lambda door, std, spd: std != "GOST 33984.1", 1650),
        (lambda door, std, spd: std == "GOST 33984.1", None)
    ],
    "R7": [
        (lambda door, std, spd: std != "GOST 33984.1", 1900),
        (lambda door, std, spd: std == "GOST 33984.1", 1950)
    ],
    "R8": [
        (lambda door, std, spd: std != "GOST 33984.1", 1950),
        (lambda door, std, spd: std == "GOST 33984.1", 1700)
    ],
    "R9": [
        (lambda door, std, spd: spd < 2, 2050),
        (lambda door, std, spd: spd >= 2, 2120)
    ],
    "R10": [
        (lambda door, std, spd: std != "GOST 33984.1", 2350),
        (lambda door, std, spd: std == "GOST 33984.1", None)
    ],
    "R11": [
        (lambda door, std, spd: std != "GOST 33984.1", 1750),
        (lambda door, std, spd: std == "GOST 33984.1", 1800)
    ],
    "R12": [
        (lambda door, std, spd: std != "GOST 33984.1", 1750),
        (lambda door, std, spd: std == "GOST 33984.1", 2500)
    ],
    "R13": [
        (lambda door, std, spd: std != "GOST 33984.1", 2050),
        (lambda door, std, spd: std == "GOST 33984.1", 2800)
    ],
    "R14": [
        (lambda door, std, spd: std != "GOST 33984.1", 1500),
        (lambda door, std, spd: std == "GOST 33984.1", 1400)
    ],
    "R15": [
        (lambda door, std, spd: std != "GOST 33984.1", 1750),
        (lambda door, std, spd: std == "GOST 33984.1", 1755)
    ],
    "R16": [
        (lambda door, std, spd: std != "GOST 33984.1", 1750),
        (lambda door, std, spd: std == "GOST 33984.1", 1790)
    ],
    "R17": [
        (lambda door, std, spd: std != "GOST 33984.1", 1750),
        (lambda door, std, spd: std == "GOST 33984.1", 2470)
    ],
    "R18": [
        (lambda door, std, spd: spd < 2, 2000),
        (lambda door, std, spd: spd >= 2, 2070)
    ],
    "R19": [
        (lambda door, std, spd: spd < 2, 2050),
        (lambda door, std, spd: spd >= 2, 2120)
    ],
    "R20": [
        (lambda door, std, spd: spd < 2, 2150),
        (lambda door, std, spd: spd >= 2, 2220)
    ],
    "R21": [
        (lambda door, std, spd: spd < 2, 2250),
        (lambda door, std, spd: spd >= 2, 2320)
    ],
    "R22": [
        (lambda door, std, spd: spd < 2, 2400),
        (lambda door, std, spd: spd >= 2, 2470)
    ]
}

SD0_table = {
        "LTHX": {
            "SIDE": {1600: "R1", 2000: "R1", 3000: "R2", 4000: "R3", 5000: "R4"},
            "REAR": {}
        },
        "LTHX Car": {
            "SIDE": {},
            "REAR": {}
        },
        "LTHW": {
            "SIDE": {1600: "R1", 2000: "R1", 3000: "R2", 4000: "R3", 5000: "R4"},
            "REAR": {}
        },
        "LTHW Car": {
            "SIDE": {},
            "REAR": {}
        },
        "EVIK": {
            "SIDE": {400: None, 630: "R11", 800: 1750, 1000: "R12", 1150: None, 1250: None, 1350: None, 1600: "R13"},
            "REAR": {400: "R5", 630: "R6", 800: "R7", 1000: "R8", 1150: "R9", 1250: "R9", 1350: None, 1600: "R10"}
        },
        "EVIN": {
            "SIDE": {400: "R14", 630: "R15", 800: "R16", 1000: "R17", 1150: 1750, 1250: 1750, 1350: 1850, 1600: 2050},
            "REAR": {}
        },
        "LTK": {
            "SIDE": {630: 1025, 800: 1025, 1000: 1025, 1150: 1025, 1250: 1025, 1350: 1025, 1600: 1175, 2000: 1175},
            "REAR": {630: 1700, 800: "R18", 1000: "R19", 1150: "R20", 1250: "R21", 1350: "R21", 1600: "R22", 2000: None}
        }
    }

def query_SD0_value(Lift_Model, Capacity, CWT_Position, Door_Opening, Standard, Speed):
    key = SD0_table.get(Lift_Model, {}).get(CWT_Position, {}).get(Capacity)
    if isinstance(key, str):
        for cond, val in lookup_rules_SD0.get(key, []):
            if cond(Door_Opening, Standard, Speed):
                return val
    else:
        return key