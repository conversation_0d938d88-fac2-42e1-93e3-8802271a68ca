lookup_rules_CD0 = {
    "a": [
        (lambda std: std == "GOST 33984.1", 1350),
        (lambda std: std != "GOST 33984.1", 1400)
    ],
    "b": [
        (lambda std: std == "GOST 33984.1", 2100),
        (lambda std: std != "GOST 33984.1", 1400)
    ],
    "c": [
        (lambda std: std == "GOST 33984.1", 2400),
        (lambda std: std != "GOST 33984.1", 1700)
    ],
    "d": [
        (lambda std: std == "GOST 33984.1", None),
        (lambda std: std != "GOST 33984.1", 1100)
    ],
    "e": [
        (lambda std: std == "GOST 33984.1", 1100),
        (lambda std: std != "GOST 33984.1", 1400)
    ],
    "f": [
        (lambda std: std == "GOST 33984.1", 2100),
        (lambda std: std != "GOST 33984.1", 1400)
    ]
}


CD0_table = {
    "LTHX": {
        "SIDE": {1600: 2100, 2000: 2100, 3000: 2500, 4000: 2800, 5000: 3100},
        "REAR": {}
    },
    "LTHX Car": {
        "SIDE": {3000: 5900, 4000: 6200, 5000: 6500},
        "REAR": {}
    },
    "LTHW": {
        "SIDE": {1600: 2100, 2000: 2100, 3000: 2500, 4000: 2800, 5000: 3100},
        "REAR": {}
    },
    "LTHW Car": {
        "SIDE": {3000: 5900, 4000: 6200, 5000: 6500},
        "REAR": {}
    },
    "EVIK": {
        "SIDE": {
            400: None, 630: 1400, 800: "a", 1000: "b", 1150: None,
            1250: None, 1350: None, 1600: "c"
        },
        "REAR": {
            400: 1000, 630: "d", 800: 1350, 1000: "e", 1150: 1400,
            1250: 1400, 1350: 1500, 1600: 1700
        }
    },
    "EVIN": {
        "SIDE": {
            400: 1000, 630: 1400, 800: 1400, 1000: "f", 1150: 1400,
            1250: 1400, 1350: 1500, 1600: 1700
        },
        "REAR": {}
    },
    "LTK": {
        "SIDE": {
            630: 1400, 800: 1400, 1000: 1600, 1150: 1700,
            1250: 1700, 1350: 1800, 1600: 2000, 2000: 2100
        },
        "REAR": {
            630: 1100, 800: 1350, 1000: 1400, 1150: 1500,
            1250: 1600, 1350: 1600, 1600: 1700, 2000: None
        }
    }
}

def query_CD0_value(Lift_Model, CWT_Position, Capacity, Standard):
    table = CD0_table.get(Lift_Model, {}).get(CWT_Position, {})
    val = table.get(Capacity, None)
    if isinstance(val, str):  # 动态规则
        return lookup_rules_CD0(val, Standard)
    else:
        return val