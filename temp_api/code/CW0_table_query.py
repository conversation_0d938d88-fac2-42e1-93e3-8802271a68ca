lookup_rules_CW0 = {
    "a": [
        (lambda std: std == "GOST 33984.1", 1400),
        (lambda std: std != "GOST 33984.1", 1350)
    ],
    "b": [
        (lambda std: std == "GOST 33984.1", 1100),
        (lambda std: std != "GOST 33984.1", 1600)
    ],
    "c": [
        (lambda std: std == "GOST 33984.1", 1400),
        (lambda std: std != "GOST 33984.1", 2000)
    ],
    "d": [
        (lambda std: std == "GOST 33984.1", None),
        (lambda std: std != "GOST 33984.1", 1400)
    ],
    "e": [
        (lambda std: std == "GOST 33984.1", 2100),
        (lambda std: std != "GOST 33984.1", 1600)
    ],
    "f": [
        (lambda std: std == "GOST 33984.1", 1100),
        (lambda std: std != "GOST 33984.1", 1600)
    ]
}


CW0_table = {
    "LTHX": {
        "SIDE": {1600: 1600, 2000: 1900, 3000: 2200, 4000: 2500, 5000: 2800},
        "REAR": {}
    },
    "LTHX Car": {
        "SIDE": {3000: 2500, 4000: 2700, 5000: 2800},
        "REAR": {}
    },
    "LTHW": {
        "SIDE": {1600: 1600, 2000: 1900, 3000: 2200, 4000: 2500, 5000: 2800},
        "REAR": {}
    },
    "LTHW Car": {
        "SIDE": {3000: 2500, 4000: 2700, 5000: 2800},
        "REAR": {}
    },
    "EVIK": {
        "SIDE": {
            400: None, 630: 1100, 800: "a", 1000: "b", 1150: None,
            1250: None, 1350: None, 1600: "c"
        },
        "REAR": {
            400: 1000, 630: "d", 800: 1400, 1000: "e", 1150: 1800,
            1250: 2000, 1350: 2000, 1600: 2000
        }
    },
    "EVIN": {
        "SIDE": {
            400: 1000, 630: 1100, 800: 1350, 1000: "f", 1150: 1800,
            1250: 2000, 1350: 2000, 1600: 2000
        },
        "REAR": {}
    },
    "LTK": {
        "SIDE": {
            630: 1100, 800: 1350, 1000: 1400, 1150: 1500,
            1250: 1600, 1350: 1600, 1600: 1700, 2000: 1900
        },
        "REAR": {
            630: 1400, 800: 1400, 1000: 1600, 1150: 1700,
            1250: 1700, 1350: 1800, 1600: 2000, 2000: None
        }
    }
}

def query_CW0_value(Lift_Model, CWT_Position, Capacity, Standard):
    table = CW0_table.get(Lift_Model, {}).get(CWT_Position, {})
    val = table.get(Capacity, None)
    if isinstance(val, str):  # 动态规则
        return lookup_rules_CW0(val, Standard)
    else:
        return val