lookup_rules_DO0 = {
    "R1": [
        (lambda std: std == "GOST 33984.1", None),
        (lambda std: std != "GOST 33984.1", "C2")
    ],
    "R2": [
        (lambda std: std == "GOST 33984.1", "S2"),
        (lambda std: std != "GOST 33984.1", "C2")
    ]
}


DO0_table = {
    "LTHX": {
        "SIDE": {1600: "S2", 2000: "S2", 3000: "S2", 4000: "C4", 5000: "C4"},
        "REAR": {}
    },
    "LTHX Car": {
        "SIDE": {3000: "C4", 4000: "C4", 5000: "C4"},
        "REAR": {}
    },
    "LTHW": {
        "SIDE": {1600: "S2", 2000: "S2", 3000: "S2", 4000: "C4", 5000: "C4"},
        "REAR": {}
    },
    "LTHW Car": {
        "SIDE": {3000: "C4", 4000: "C4", 5000: "C4"},
        "REAR": {}
    },
    "EVIK": {
        "SIDE": {
            400: None, 630: "C2", 800: "R2", 1000: "R2", 1150: "S2",
            1250: "S2", 1350: "S2", 1600: "R2"
        },
        "REAR": {
            400: "C2", 630: "R1", 800: "R2", 1000: "R2", 1150: "C2",
            1250: "C2", 1350: "C2", 1600: "C2"
        }
    },
    "EVIN": {
        "SIDE": {
            400: "C2", 630: "C2", 800: "R2", 1000: "R2", 1150: "C2",
            1250: "C2", 1350: "C2", 1600: "C2"
        },
        "REAR": {}
    },
    "LTK": {
        "SIDE": {
            630: "C2", 800: "C2", 1000: "C2", 1150: "C2",
            1250: "C2", 1350: "C2", 1600: "C2", 2000: "C2"
        },
        "REAR": {
            630: "C2", 800: "C2", 1000: "C2", 1150: "C2",
            1250: "C2", 1350: "C2", 1600: "C2", 2000: None
        }
    }
}

def query_DO0_value(Lift_Model, CWT_Position, Capacity, Standard):
    table = DO0_table.get(Lift_Model, {}).get(CWT_Position, {})
    val = table.get(Capacity, None)
    if isinstance(val, str) and val.startswith("R"):  # 动态规则
        return lookup_rules_DO0(val, Standard)
    else:
        return val