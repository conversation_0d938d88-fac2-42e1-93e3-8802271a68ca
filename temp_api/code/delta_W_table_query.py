look_up_rules_W = {
    "a": [
        (lambda dw: dw <= 700, {"W": 70, "WR": 70}),
        (lambda dw: dw > 700,  {"W": 60, "WR": 60}),
    ]
}

delta_W_table = {
    "GOST": {
        "C2": {"W": "a", "WR": "a", "CDSW": 75},
        "S2": {"W": 73, "WR": 100, "CDSW": 90}
    },
    "non-GOST": {
        "C2": {"W": 75, "WR": 75, "CDSW": 60},
        "S2": {"W": 92, "WR": 140, "CDSW": 96},
        "C4": {"W": 107, "WR": 107, "CDSW": 96},
        "S3": {"W": 107, "WR": 185, "CDSW": 132},
        "C6": {"W": 125, "WR": 125, "CDSW": 132}
    }
}

def query_W_value(Standard, Door_Opening, component, Door_Width):
    std_key = "GOST" if Standard == "GOST 33984.1" else "non-GOST"
    entry = delta_W_table.get(std_key, {}).get(Door_Opening)
    if not entry:
        return None
    else:
        value = entry.get(component)
        if isinstance(value, str):  # 如果是变量规则，如 "a"
            for cond, out in look_up_rules_W[value]:
                if cond(Door_Width):
                    return out[component]
            return None
        else:
            return value