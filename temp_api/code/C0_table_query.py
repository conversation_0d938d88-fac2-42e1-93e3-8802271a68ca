lookup_rules_C0 = {
    "R1": [
        (lambda std: std == "GOST 33984.1", None),
        (lambda std: std != "GOST 33984.1", 0)
    ],
    "R2": [
        (lambda std: std == "GOST 33984.1", "Offset"),
        (lambda std: std != "GOST 33984.1", 0)
    ]
}


C0_table = {
    "LTHX": {
        "SIDE": {1600: "Offset", 2000: "Offset", 3000: "Offset", 4000: "Offset", 5000: "Offset"},
        "REAR": {}
    },
    "LTHX Car": {
        "SIDE": {3000: 0, 4000: 0, 5000: 0},
        "REAR": {}
    },
    "LTHW": {
        "SIDE": {1600: "Offset", 2000: "Offset", 3000: "Offset", 4000: "Offset", 5000: "Offset"},
        "REAR": {}
    },
    "LTHW Car": {
        "SIDE": {3000: 5900, 4000: 6200, 5000: 6500},
        "REAR": {}
    },
    "EVIK": {
        "SIDE": {
            400: None, 630: "Offset", 800: "Offset", 1000: "Offset", 1150: "Offset",
            1250: "Offset", 1350: "Offset", 1600: "Offset"
        },
        "REAR": {
            400: 0, 630: "R1", 800: "R2", 1000: "R2", 1150: 0,
            1250: 0, 1350: 0, 1600: 0
        }
    },
    "EVIN": {
        "SIDE": {
            400: "Offset", 630: "Offset", 800: "Offset", 1000: "R2", 1150: 0,
            1250: 0, 1350: 0, 1600: 0
        },
        "REAR": {}
    },
    "LTK": {
        "SIDE": {
            630: "Offset", 800: "Offset", 1000: "Offset", 1150: "Offset",
            1250: "Offset", 1350: "Offset", 1600: "Offset", 2000: "Offset"
        },
        "REAR": {
            630: 0, 800: 0, 1000: 0, 1150: 0,
            1250: 0, 1350: 0, 1600: 0, 2000: None
        }
    }
}

def query_C0_value(Lift_Model, CWT_Position, Capacity, Standard):
    table = C0_table.get(Lift_Model, {}).get(CWT_Position, {})
    val = table.get(Capacity, None)
    if isinstance(val, str) and val.startswith("R"):  # 动态规则
        return lookup_rules_C0(val, Standard)
    else:
        return val