lookup_rules_SW0 = {
    "a": [
        (lambda std: std == "GOST 33984.1", 1750),
        (lambda std: std != "GOST 33984.1", 1800)
    ],
    "b": [
        (lambda std: std == "GOST 33984.1", 1650),
        (lambda std: std != "GOST 33984.1", 2200)
    ],
    "c": [
        (lambda std: std == "GOST 33984.1", 2050),
        (lambda std: std != "GOST 33984.1", 2700)
    ],
    "d": [
        (lambda std: std == "GOST 33984.1", None),
        (lambda std: std != "GOST 33984.1", 1820)
    ],
    "e": [
        (lambda std: std == "GOST 33984.1", 1800),
        (lambda std: std != "GOST 33984.1", 1820)
    ],
    "f": [
        (lambda std: std == "GOST 33984.1", 2470),
        (lambda std: std != "GOST 33984.1", 2000)
    ],
    "g": [
        (lambda std: std == "GOST 33984.1", None),
        (lambda std: std != "GOST 33984.1", 2400)
    ],
    "h": [
        (lambda std: std == "GOST 33984.1", 1900),
        (lambda std: std != "GOST 33984.1", 1975)
    ],
    "i": [
        (lambda std: std == "GOST 33984.1", 1650),
        (lambda std: std != "GOST 33984.1", 2225)
    ]
}


SW0_table = {
    "LTHX": {
        "SIDE": {1600: 2450, 2000: 2800, 3000: 3150, 4000: 3700, 5000: 4100},
        "REAR": {}
    },
    "LTHX Car": {
        "SIDE": {},
        "REAR": {}
    },
    "LTHW": {
        "SIDE": {1600: 2650, 2000: 2950, 3000: 3400, 4000: 3950, 5000: 4250},
        "REAR": {}
    },
    "LTHW Car": {
        "SIDE": {},
        "REAR": {}
    },
    "EVIK": {
        "SIDE": {
            400: None, 630: "a", 800: 1950, 1000: "b", 1150: None,
            1250: None, 1350: None, 1600: "c"
        },
        "REAR": {
            400: 1550, 630: "d", 800: "e", 1000: "f", 1150: 2250,
            1250: 2450, 1350: 2450, 1600: "g"
        }
    },
    "EVIN": {
        "SIDE": {
            400: 1550, 630: "a", 800: "h", 1000: "i", 1150: 2475,
            1250: 2705, 1350: 2705, 1600: 2705
        },
        "REAR": {}
    },
    "LTK": {
        "SIDE": {
            630: 1900, 800: 2150, 1000: 2200, 1150: 2300,
            1250: 2400, 1350: 2400, 1600: 2550, 2000: 2750
        },
        "REAR": {
            630: 1900, 800: 1900, 1000: 2100, 1150: 2200,
            1250: 2200, 1350: 2300, 1600: 2500, 2000: None
        }
    }
}

def query_SW0_value(Lift_Model, CWT_Position, Capacity, Standard):
    table = SW0_table.get(Lift_Model, {}).get(CWT_Position, {})
    val = table.get(Capacity, None)
    if isinstance(val, str):  # 动态规则
        return lookup_rules_SW0(val, Standard)
    else:
        return val