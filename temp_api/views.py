from rest_framework.views import APIView
from core.resp import make_response
from temp_api.code.calculate import calculate

required_fields =     [
    "Lift_Model",
    "Capacity",
    "Speed",
    "Travel_Height",
    "Car_Width",
    "Car_Depth",
    "Car_Height",
    "CWT_Position",
    "CWT_Safety_Gear",
    "Door_Opening",
    "Door_Width",
    "Door_Height",
    "Through_Door",
    "Glass_Door",
    "Standard",
    "Door_Center_from_Car_Center",
    "Car_Area_Exceed_the_Code",
    "Shaft_Tolerance",
    "Marble_Floor",
    "Shaft_Width",
    "Shaft_Depth",
    "Overhead",
    "Pit_Depth"
                       ]


# 计算电梯选型
class CalculationElevatorParameters(APIView):

    authentication_classes = []
    permission_classes = []

    def post(self, request):

        data = request.data.copy()

        for field in required_fields:

            if field not in data:

                return make_response(code=-1,msg="缺少必要参数")

        res = calculate(data)
        return make_response(code=0,msg="Success",data=res)



# 计算推荐值
class CalculationRecommendParameters(APIView):

    authentication_classes = []
    permission_classes = []

    def post(self, request):
        
        data = request.data.copy()

        for field in required_fields:

            if field not in data:

                return make_response(code=-1,msg="缺少必要参数")

        res = calculate(data)
        
        Shaft_Depth_min = res.get("Shaft_Depth_min",None)
        Shaft_Height_min = res.get("Shaft_Height_min",None)
        Shaft_Pit_min = res.get("Shaft_Pit_min",None)
        Shaft_Width_min = res.get("Shaft_Width_min",None)
        warn = res.get("warn",False)
        warning_text = res.get("warning_text",[])
        
        
        if not Shaft_Depth_min or not Shaft_Height_min or not Shaft_Pit_min or not Shaft_Width_min:
            return make_response(code=-1,msg="缺少必要参数")
        
        
        res = {
            "Shaft_Depth_min":Shaft_Depth_min,
            "Shaft_Height_min":Shaft_Height_min,
            "Shaft_Pit_min":Shaft_Pit_min,
            "Shaft_Width_min":Shaft_Width_min,
            "warn":warn,
            "warning_text":warning_text if warning_text else []
        }
        
        
        
        return make_response(code=0,msg="Success",data=res)
