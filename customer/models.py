from django.db import models

from core.generate_resource_id import generate_contact_id, generate_customer_id, generate_resource_ulid
from core.model import BaseModel
from customer.enums import CustomerImportance, CustomerType




# 客户模型
class Customer(BaseModel):
    
    # 客户类型
    customer_type = models.CharField(max_length=50, choices=CustomerType.choices,verbose_name='客户类型')
    # 客户名称
    name = models.CharField(max_length=50,verbose_name='客户名称')
    # 客户电话
    phone = models.CharField(max_length=50,verbose_name='客户电话')
    # 客户地址
    address = models.CharField(max_length=255,verbose_name='客户地址')
    # 客户邮箱
    email = models.EmailField(max_length=255,verbose_name='客户邮箱')
    # 客户重要程度
    importance = models.CharField(max_length=50, choices=CustomerImportance.choices,verbose_name='客户重要程度')
    # 客户备注
    remark = models.TextField(verbose_name='客户备注')
    # 客户id
    customer_id = models.CharField(max_length=27,verbose_name='客户id',unique=True,default=generate_customer_id,blank=True)
    
    class Meta:
        verbose_name = '客户信息'
        verbose_name_plural = '客户信息'

    def __str__(self):
        return self.name


# 客户联系人
class CustomerContact(BaseModel):
    # 客户
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='contacts')
    # 联系人姓名
    contact_name = models.CharField(max_length=50)
    # 联系人电话
    contact_phone = models.CharField(max_length=50)
    # 联系人id
    contact_id = models.CharField(max_length=27,verbose_name='联系人id',unique=True,default=generate_contact_id,blank=True)
    
    class Meta:
        verbose_name = '客户联系人'
        verbose_name_plural = '客户联系人'
    
    def __str__(self):
        return self.contact_name